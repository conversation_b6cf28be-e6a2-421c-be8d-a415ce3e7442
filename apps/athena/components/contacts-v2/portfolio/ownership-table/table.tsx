import { PropsWithChildren, useMemo } from 'react';
import { Button, Typography } from '@ds';
import { Badge, ProgressBar } from '@ds';
import {
  ArrowRightIcon,
  ChevronDownIcon,
  PlusIcon,
} from '@heroicons/react/outline';
import { clsx } from 'clsx';
import dayjs from 'dayjs';
import { useRouter } from 'next/router';
import { Contact } from '@/apollo/generated';
import { useContactProfileContext } from '@/components/contacts-v2/contacts-context';
import TableHeader, { Header } from '@/components/utils/tables/table-header';
import { useCurrentCompanyProfileUser } from '@/contexts/current-company-profile-user-context';
import { formatMoney } from '@/utils/common-helpers';

interface Props {
  contact: Contact;
  setShowBeneficialOwnerAccounts: (show: boolean) => void;
  setShowShareholdings: (show: boolean) => void;
  showBeneficialOwnerAccounts: boolean;
  showShareholdings: boolean;
}

const SelectedTypography = ({
  children,
  isSelected = false,
}: PropsWithChildren & {
  isSelected: boolean;
}) => (
  <Typography
    className={clsx('truncate', isSelected && 'text-amplify-green-700')}
    variant={isSelected ? 'text-label-sm' : 'text-body-sm'}
  >
    {children}
  </Typography>
);

const Table: React.FC<Props> = ({
  contact,
  setShowBeneficialOwnerAccounts,
  setShowShareholdings,
  showBeneficialOwnerAccounts,
  showShareholdings,
}) => {
  const {
    beneficialOwnerDataForSidebar,
    shareholderDataForSidebar,
    toggleOpenLinkShareholdingsModal,
    totalBeneficialOwnerAccountShares,
    totalShareholdings,
  } = useContactProfileContext();

  const columns: Header[] = [
    {
      className: 'min-w-[120px] max-w-[235px] pr-2',
      label: 'Account name',
    },
    {
      className: 'min-w-[124px] pr-2',
      label: 'Type',
    },
    {
      className: 'min-w-[80px] pr-2',
      label: 'Last updated',
    },
    {
      className: 'min-w-[80px] pr-2 text-right',
      label: 'Shares (#)',
    },
    {
      className: 'min-w-[80px] pr-2 text-right',
      label: 'Value ($)',
      tooltip: {
        description: 'Value of holdings based on latest closing price.',
      },
    },
    {
      className: 'min-w-[100px] max-w-[110px] pr-8 text-right',
      label: 'Portfolio (%)',
    },
    // TODO: not deleting as we will use this again shortly when BO is implemented
    // {
    //   className: 'min-w-[100px] ml-10',
    //   label: 'Held under',
    // },
    {
      className: 'w-[30px]',
      label: '',
    },
  ];

  const { price } = useCurrentCompanyProfileUser();

  const shareholdings = useMemo(() => {
    if (!contact || !contact.shareholdings) return [];

    let shareholdingsToSort = contact.shareholdings.filter(
      (sh) => sh.shareCount !== null
    );

    if (shareholdingsToSort.length > 1) {
      shareholdingsToSort = shareholdingsToSort.sort((a, b) => {
        return Number(b.shareCount) - Number(a.shareCount);
      });
    }

    return shareholdingsToSort;
  }, [contact]);

  const beneficialOwnerAccounts = useMemo(() => {
    if (!contact || !contact.beneficialOwnerAccounts) return [];
    return contact.beneficialOwnerAccounts;
  }, [contact]);

  const currency = contact?.shareholdings[0]?.currency || '';

  if (!contact || !contact.shareholdings) return null;

  const renderBody = () => {
    return (
      <>
        <AccountTypeHeaderRow
          currency={currency}
          numberOfAccounts={contact.shareholdings.length}
          setShow={setShowShareholdings}
          show={showShareholdings}
          title="Registry accounts"
          totalCombinedShares={
            totalShareholdings + totalBeneficialOwnerAccountShares
          }
          totalSectionShares={totalShareholdings}
          totalSectionValue={(price as number) * totalShareholdings}
        />
        {shareholdings.map((sh) => {
          if (!sh) return;

          const isSelected = sh.id === shareholderDataForSidebar?.id;

          return (
            <AccountRow
              key={`${sh.id}-registry`}
              accountName={sh.accountName}
              currency={currency}
              id={sh.id}
              isSelected={isSelected}
              lastUpdatedAt={sh.updatedAt as string}
              price={price as number}
              shares={sh.shareCount as number}
              show={showShareholdings}
              totalShareholdings={totalShareholdings}
              type="Registry"
            />
          );
        })}
        <AccountTypeHeaderRow
          className={clsx(!showBeneficialOwnerAccounts && '!border-none')}
          currency={currency}
          numberOfAccounts={contact.beneficialOwnerAccounts.length}
          setShow={setShowBeneficialOwnerAccounts}
          show={showBeneficialOwnerAccounts}
          title="Unmasked accounts"
          totalCombinedShares={
            totalBeneficialOwnerAccountShares + totalShareholdings
          }
          totalSectionShares={totalBeneficialOwnerAccountShares}
          totalSectionValue={
            (price as number) * totalBeneficialOwnerAccountShares
          }
        />
        {beneficialOwnerAccounts.map((bo) => {
          const latestHolding = bo.latestHolding;

          const hasChildren =
            latestHolding?.children && latestHolding.children.length > 0;

          return (
            <AccountRow
              key={`${bo.id}-beneficial-owner`}
              accountName={bo.accountName}
              currency={currency}
              id={bo.id}
              isSelected={bo.id === beneficialOwnerDataForSidebar?.id}
              lastUpdatedAt={latestHolding?.updatedAt as string}
              price={price as number}
              shares={latestHolding?.shares as number}
              show={showBeneficialOwnerAccounts}
              totalShareholdings={totalBeneficialOwnerAccountShares}
              type={hasChildren ? 'Intermediary' : 'Beneficial owner'}
            />
          );
        })}
      </>
    );
  };

  return (
    <div>
      <div data-private className="min-w-full rounded-lg">
        <div className="relative max-h-[398px] overflow-auto">
          <table className="table min-w-full">
            <TableHeader
              className="shadow-xs sticky top-0 z-10 bg-white"
              headers={columns}
              textColor="text-gray-500"
              textVariant="text-button-sm"
            />
            <tbody className="table-row-group overflow-y-auto rounded-lg border-t">
              {renderBody()}
            </tbody>
          </table>
        </div>
        <div className="flex w-full items-center justify-center border-t p-4">
          <Button
            LeadingIcon={PlusIcon}
            className="text-gray-500"
            variant="link-color"
            onClick={() => toggleOpenLinkShareholdingsModal(true)}
          >
            Add account
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Table;

const AccountRow = ({
  accountName,
  currency,
  id,
  isSelected,
  lastUpdatedAt,
  price,
  shares,
  show,
  totalShareholdings,
  type,
}: {
  accountName: string;
  currency: string;
  id: string;
  isSelected: boolean;
  lastUpdatedAt: string;
  price: number;
  shares: number;
  show: boolean;
  totalShareholdings: number;
  type: 'Registry' | 'Beneficial owner' | 'Intermediary';
}) => {
  const { pathname, push, query } = useRouter();

  const queryWithoutIds = useMemo(() => {
    const { accountId, shareholdingId, ...rest } = query;
    return rest;
  }, [query]);

  const lastUpdatedAtFormatted = useMemo(() => {
    if (!lastUpdatedAt) return '-';

    const now = dayjs();

    if (dayjs(lastUpdatedAt).isSame(now, 'day')) {
      return 'Today';
    }

    if (
      dayjs(lastUpdatedAt).isBefore(now, 'day') &&
      dayjs(lastUpdatedAt).isAfter(now.subtract(7, 'day'), 'day')
    ) {
      const dayDiff = Math.abs(dayjs(lastUpdatedAt).diff(now, 'day'));
      return dayDiff === 0 ? 'Today' : `${dayDiff} days ago`;
    }

    return dayjs(lastUpdatedAt).format('DD/MM/YYYY');
  }, [lastUpdatedAt]);

  return (
    <tr
      key={`${accountName}-${type}`}
      className={clsx(
        isSelected && 'bg-amplify-green-25',
        !isSelected && 'bg-white hover:cursor-pointer hover:bg-gray-50',
        'table-row border-b first:border-t last:border-0   md:first:border-t-0',
        !show && 'hidden'
      )}
      onClick={() =>
        push(
          {
            pathname,
            query:
              type === 'Registry'
                ? { ...queryWithoutIds, shareholdingId: `${id}` }
                : { ...queryWithoutIds, accountId: `${id}` },
          },
          undefined,
          {
            shallow: true,
          }
        )
      }
    >
      <td className="min-w-[120px] max-w-[235px] truncate py-[14px] pl-14">
        <SelectedTypography isSelected={isSelected}>
          {accountName}
        </SelectedTypography>
      </td>
      <td className="pl-2">
        {type === 'Registry' ? (
          <Badge color="fuchsia" size="sm">
            Registry
          </Badge>
        ) : type === 'Intermediary' ? (
          <Badge color="moss" size="sm">
            Intermediary
          </Badge>
        ) : (
          <Badge color="moss" size="sm">
            Beneficial Owner
          </Badge>
        )}
      </td>
      <td className="pl-3">
        <SelectedTypography isSelected={isSelected}>
          {lastUpdatedAtFormatted}
        </SelectedTypography>
      </td>
      <td className="pr-3 text-right">
        <SelectedTypography isSelected={isSelected}>
          {shares?.toLocaleString()}
        </SelectedTypography>
      </td>
      <td className="pr-3 text-right">
        <SelectedTypography isSelected={isSelected}>
          {formatMoney(shares * (price as number), currency)}
        </SelectedTypography>
      </td>
      <td className="pr-8 text-right">
        <div className="flex items-center justify-end gap-2">
          <SelectedTypography isSelected={isSelected}>
            {(() => {
              const denominator = totalShareholdings;
              if (denominator === 0 || isNaN(shares / denominator)) return '0%';
              return `${Math.round((shares / denominator) * 100)}%`;
            })()}
          </SelectedTypography>
          <ProgressBar max={totalShareholdings} value={shares} width={40} />
        </div>
      </td>
      <td className="pr-6 text-right">
        <ArrowRightIcon className="h-4 w-4 text-green-700" />
      </td>
    </tr>
  );
};

const AccountTypeHeaderRow = ({
  className,
  currency,
  numberOfAccounts,
  setShow,
  show,
  title,
  totalCombinedShares,
  totalSectionShares,
  totalSectionValue,
}: {
  className?: string;
  currency: string;
  numberOfAccounts: number;
  setShow: (show: boolean) => void;
  show: boolean;
  title: string;
  totalCombinedShares: number;
  totalSectionShares: number;
  totalSectionValue: number;
}) => {
  return (
    <tr
      key={title}
      className={clsx('table-row border-b', className)}
      onClick={() => setShow(!show)}
    >
      <td className="min-w-[120px] max-w-[235px] truncate py-[14px] pl-4">
        <div className="flex items-center gap-3">
          <div
            className={clsx(
              'flex items-center justify-center rounded-xl border p-1.5 transition-transform duration-300',
              show && 'rotate-180'
            )}
          >
            <ChevronDownIcon className="h-4 w-4 text-gray-700" />
          </div>
          <div>
            <div className="flex items-center gap-2">
              <Typography variant="text-label-sm">{title}</Typography>
              <Badge size="sm">{numberOfAccounts}</Badge>
            </div>
          </div>
        </div>
      </td>
      <td className="pl-2" />
      <td className="pl-2" />
      <td className="pr-3 text-right">
        <Typography variant="text-label-sm">
          {totalSectionShares.toLocaleString()}
        </Typography>
      </td>
      <td className="pr-3 text-right">
        <Typography variant="text-label-sm">
          {formatMoney(totalSectionValue, currency)}
        </Typography>
      </td>
      <td className="pr-8 text-right">
        <div className="flex items-center justify-end gap-2">
          <Typography variant="text-label-sm">
            {totalSectionShares === 0
              ? 0
              : Math.round((totalSectionShares / totalCombinedShares) * 100)}
            %
          </Typography>
          <ProgressBar
            max={totalCombinedShares}
            value={totalSectionShares}
            width={40}
          />
        </div>
      </td>
      <td className="pr-6 text-right">
        <ArrowRightIcon className="h-4 w-4 text-white" />
      </td>
    </tr>
  );
};
