import React, {
  ReactElement,
  SVGProps,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { Badge, TextInput, Button, Skeleton, Tooltip, Typography } from '@ds';
import {
  ClockIcon,
  CurrencyDollarIcon,
  HashtagIcon,
  InformationCircleIcon,
  MinusCircleIcon,
  PlusCircleIcon,
  ScaleIcon,
  XIcon,
  ArrowUpIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  MailIcon,
  PhoneIcon,
  HomeIcon,
  ArrowDownIcon,
  ArrowRightIcon,
} from '@heroicons/react/outline';
import { MagnifyingGlassIcon } from '@heroicons/react-v2/24/outline';
import clsx from 'clsx';
import dayjs from 'dayjs';
import Link from 'next/link';
import { useRouter } from 'next/router';
import {
  ShareMovement,
  SortOrder,
  useShareholdingSummaryLazyQuery,
} from '@/apollo/generated';
import { useContactProfileContext } from '@/components/contacts-v2/contacts-context';
import { useCurrentCompanyProfileUser } from '@/contexts/current-company-profile-user-context';
import { formatMoney } from '@/utils/common-helpers';
import { singleLineAddress } from '@/utils/contacts-helper';
import { holdingInYearsAndMonths } from '@/utils/registry-helper';
import routes from '@/utils/routes';

const SingleShareholdingInfoRightSidebar: React.ComponentType = () => {
  const { pathname, push, query } = useRouter();
  const [showMoreShareholdingInfo, setShowMoreShareholdingInfo] =
    useState(false);
  const [showContactInfo, setShowContactInfo] = React.useState(true);
  const [showTransactions, setShowTransactions] = React.useState(true);
  const [isAtTopTransactions, setIsAtTopTransactions] = useState(true);
  const [isAtBottomTransactions, setIsAtBottomTransactions] = useState(false);
  const [isAtTopRetailHolders, setIsAtTopRetailHolders] = useState(true);
  const [isAtBottomRetailHolders, setIsAtBottomRetailHolders] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showRetailOwners, setShowRetailOwners] = useState(false);

  const {
    currentCompanyProfileUser: {
      profile: {
        currency,
        ticker: { marketListingKey },
      },
    },
    isUK,
    price,
  } = useCurrentCompanyProfileUser();

  const {
    shareholderDataForSidebar: shareholding,
    toggleOpenLinkContactModal,
  } = useContactProfileContext();

  const [getShareholdingSummary, { data, loading }] =
    useShareholdingSummaryLazyQuery();

  const children = useMemo(() => {
    if (!data) return [];

    if (searchQuery && searchQuery.length > 0)
      return data?.shareholdingSummary?.beneficialOwnerHoldings?.filter((b) => {
        return (b?.beneficialOwnerAccount?.accountName ?? '')
          .toLowerCase()
          .includes(searchQuery.toLowerCase());
      });

    return data?.shareholdingSummary?.beneficialOwnerHoldings;
  }, [data, searchQuery]);

  useEffect(() => {
    if (shareholding?.id) {
      getShareholdingSummary({
        variables: {
          endDate: dayjs().format('YYYY-MM-DD'),
          id: shareholding?.id as string,
          movementSortOrder: SortOrder.Desc,
          startDate: shareholding?.initialPurchaseDate
            ? shareholding.initialPurchaseDate
            : '1900-01-01',
        },
      });
    }
  }, [
    shareholding?.id,
    shareholding?.initialPurchaseDate,
    getShareholdingSummary,
  ]);

  const formattedAddress = useMemo(() => {
    if (!shareholding) return null;
    return singleLineAddress(shareholding);
  }, [shareholding]);

  const totalBoughtSold = useMemo(() => {
    if (!data) return null;
    const totals = data?.shareholdingSummary?.shareMovements.reduce(
      (acc, movement) => {
        if (!movement) return acc;

        if (movement.movement > 0) {
          acc.bought += movement.movement;
        } else {
          acc.sold += movement.movement;
        }
        return acc;
      },
      { bought: 0, sold: 0 }
    );
    return totals;
  }, [data]);

  const handleScrollTransactions = (e: React.UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement;
    const isTop = target.scrollTop === 0;
    const isBottom =
      Math.abs(target.scrollHeight - target.scrollTop - target.clientHeight) <
      1;

    setIsAtTopTransactions(isTop);
    setIsAtBottomTransactions(isBottom);
  };

  const handleScrollRetailHolders = (e: React.UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement;
    const isTop = target.scrollTop === 0;
    const isBottom =
      Math.abs(target.scrollHeight - target.scrollTop - target.clientHeight) <
      1;

    setIsAtTopRetailHolders(isTop);
    setIsAtBottomRetailHolders(isBottom);
  };

  const renderHoldings = useCallback(() => {
    if (!shareholding) return null;

    return (
      <Row LeadingIcon={ScaleIcon} label="Holdings">
        <div className="flex flex-col items-start justify-center">
          <Typography variant="text-label-sm">
            {shareholding?.shareCount?.toLocaleString()}
          </Typography>
          <Typography className="text-wrap break-all" variant="text-body-sm">
            (
            {formatMoney(
              price && shareholding?.shareCount
                ? shareholding?.shareCount * price
                : 0,
              currency ?? ''
            )}
            )
          </Typography>
        </div>
      </Row>
    );
  }, [shareholding, price, currency]);

  const profit = useMemo(() => {
    // If we have the latest price, recalculate profit loss using latest price
    // Else use the profit loss calculation from previous eod
    const profitLoss = shareholding?.estimatedProfitLoss || 0;
    const purchaseValue = shareholding?.estimatedTotalPurchaseValue || 0;
    const saleValue = shareholding?.estimatedTotalSaleValue || 0;
    const shareCount = shareholding?.shareCount || 0;

    const total = price
      ? price * shareCount + saleValue - purchaseValue
      : profitLoss;

    const percent = total / purchaseValue || null;

    return { percent, total };
  }, [price, shareholding]);

  const renderProfitLoss = useCallback(() => {
    if (!shareholding) return null;

    const isProfit = profit.total > 0;
    const isLoss = profit.total < 0;

    let profitLoss;
    if (isProfit) {
      profitLoss = (
        <Typography className="text-green-700" variant="text-label-sm">
          +{profit.total.toLocaleString()}
        </Typography>
      );
    } else if (isLoss) {
      profitLoss = (
        <Typography className="text-red-600" variant="text-label-sm">
          -{Math.abs(profit.total).toLocaleString()}
        </Typography>
      );
    } else {
      profitLoss = (
        <Typography variant="text-label-sm">
          {formatMoney(0, currency ?? '')}
        </Typography>
      );
    }

    return (
      <Row
        LeadingIcon={CurrencyDollarIcon}
        label={isUK ? 'Market value change' : 'Profit / loss'}
      >
        <div className="space-y-2">
          {!isUK && (
            <div className="flex items-center gap-1">
              <Typography
                className={clsx(
                  isProfit && 'text-green-600',
                  isLoss && 'text-red-600'
                )}
                variant="text-body-sm"
              >
                {isProfit ? 'In profit' : isLoss ? 'In loss' : 'No gain/loss'}
              </Typography>
              <Tooltip
                description="Estimated unrealised profit at last closing price."
                id="profit-loss-tooltip"
              >
                <InformationCircleIcon className="h-4 w-4" />
              </Tooltip>
            </div>
          )}
          <div className="flex flex-col items-start justify-center gap-1 text-wrap break-all">
            {profitLoss}
            {!!profit.percent &&
              shareholding.estimatedTotalPurchaseValue !== 0 && (
                <Badge size="sm">
                  <div className="flex items-center gap-1">
                    {profit?.percent?.toFixed(2)}%
                  </div>
                </Badge>
              )}

            <Badge size="sm">
              <div className="flex items-center gap-1">
                {(100.0).toFixed(2)}%
              </div>
            </Badge>
          </div>
        </div>
      </Row>
    );
  }, [shareholding, currency, isUK, profit]);

  const renderTimeHeld = useCallback(() => {
    if (!shareholding) return null;
    if (!shareholding?.currentHoldingStartDate) return null;

    return (
      <Row
        LeadingIcon={ClockIcon}
        label={isUK ? 'Current holding period' : 'Time held'}
      >
        <div>
          <Typography variant="text-label-sm">
            {holdingInYearsAndMonths(
              shareholding?.currentHoldingStartDate,
              true
            )}
          </Typography>
          <Typography className="text-gray-700" variant="text-body-sm">
            since{' '}
            {dayjs(shareholding?.currentHoldingStartDate).format('DD MMM YYYY')}
          </Typography>
        </div>
      </Row>
    );
  }, [shareholding, isUK]);

  const renderLastMovement = useCallback(() => {
    if (loading) return <Skeleton loading height={20} />;
    if (!shareholding) return null;
    if (!data) return null;

    const latestMovement = data?.shareholdingSummary?.shareMovements[0];

    return (
      <Row LeadingIcon={ArrowUpIcon} label="Latest movement">
        <div className="flex flex-col items-start justify-center">
          <Typography variant="text-label-sm">
            {formatMoney(latestMovement?.movement ?? 0, '')}
          </Typography>
          <Typography className="text-wrap break-all" variant="text-body-sm">
            (
            {formatMoney(
              price && latestMovement?.movement
                ? latestMovement?.movement * price * 1000000
                : 0,
              currency ?? ''
            )}
            )
          </Typography>
        </div>
      </Row>
    );
  }, [loading, shareholding, data, price, currency]);

  const renderTotalBought = useCallback(() => {
    if (loading) return <Skeleton loading height={20} />;
    if (!shareholding) return null;
    if (!data) return null;

    return (
      <Row LeadingIcon={PlusCircleIcon} label="Total bought">
        <div className="flex flex-col items-start justify-center">
          <Typography variant="text-label-sm">
            {formatMoney(totalBoughtSold?.bought ?? 0, '')}
          </Typography>
          <Typography className="text-wrap break-all" variant="text-body-sm">
            (
            {formatMoney(
              shareholding?.estimatedTotalPurchaseValue || 0,
              currency ?? ''
            )}
            )
          </Typography>
        </div>
      </Row>
    );
  }, [loading, shareholding, data, totalBoughtSold, currency]);

  const renderTotalSold = useCallback(() => {
    if (loading) return <Skeleton loading height={20} />;
    if (!shareholding) return null;
    if (!data) return null;

    return (
      <Row LeadingIcon={MinusCircleIcon} label="Total sold">
        <div className="flex flex-col items-start justify-center">
          <Typography variant="text-label-sm">
            {formatMoney(totalBoughtSold?.sold || 0, '')}
          </Typography>
          <Typography className="text-wrap break-all" variant="text-body-sm">
            (
            {formatMoney(
              shareholding?.estimatedTotalSaleValue || 0,
              currency ?? ''
            )}
            )
          </Typography>
        </div>
      </Row>
    );
  }, [loading, shareholding, data, totalBoughtSold, currency]);

  const renderTotalTrades = useCallback(() => {
    if (loading) return <Skeleton loading height={20} />;
    if (!shareholding) return null;
    if (!data) return null;

    return (
      <Row LeadingIcon={HashtagIcon} label="Total trades">
        <div className="flex items-center gap-2">
          <Typography variant="text-label-sm">
            {data?.shareholdingSummary?.shareMovements?.length}
          </Typography>
        </div>
      </Row>
    );
  }, [loading, shareholding, data]);

  const renderContent = useMemo(() => {
    return (
      <div className="relative h-full w-full max-w-[320px] space-y-4">
        <div className="flex items-center justify-between">
          <Typography variant="text-heading-md">
            {shareholding?.accountName}
          </Typography>
          <Button
            size="sm"
            variant="link-gray"
            onClick={() => {
              const { accountId, shareholdingId, ...rest } = query;
              push(
                {
                  pathname,
                  query: {
                    ...rest,
                  },
                },
                undefined,
                {
                  shallow: true,
                }
              );
            }}
          >
            <XIcon className="h-4 w-4" />
          </Button>
        </div>
        <Badge color="fuchsia" size="sm">
          Register account
        </Badge>
        {/* Shareholding info */}
        <section className="bg-gray-25 rounded-lg border p-4">
          <div className="space-y-4">
            {renderHoldings()}
            {renderProfitLoss()}
            {renderTimeHeld()}
            {renderLastMovement()}
            <div
              className={clsx(
                `space-y-4 overflow-hidden transition-[max-height] duration-300 ease-in-out`,
                showMoreShareholdingInfo ? 'max-h-[500px]' : 'max-h-0'
              )}
            >
              {renderTotalBought()}
              {renderTotalSold()}
              {renderTotalTrades()}
            </div>
            <div className="flex justify-center">
              <Button
                TrailingIcon={
                  showMoreShareholdingInfo ? ChevronUpIcon : ChevronDownIcon
                }
                variant="link-color"
                onClick={() =>
                  setShowMoreShareholdingInfo(!showMoreShareholdingInfo)
                }
              >
                {showMoreShareholdingInfo ? 'See less' : 'See more'}
              </Button>
            </div>
          </div>
        </section>
        <div className="ml-[-24px] h-px w-[calc(100%+48px)] bg-gray-200" />
        {/* Contact info */}
        <section>
          <div className="flex items-center justify-between">
            <Typography variant="text-heading-sm">Contact info</Typography>
            <div
              className="ml-2 cursor-pointer transition-transform duration-300"
              onClick={() => setShowContactInfo(!showContactInfo)}
            >
              {showContactInfo ? (
                <ChevronUpIcon className="h-4 w-4" />
              ) : (
                <ChevronDownIcon className="h-4 w-4" />
              )}
            </div>
          </div>
          <div
            className={`space-y-4 overflow-hidden transition-[max-height,margin-top,opacity] duration-300 ease-in-out ${
              showContactInfo
                ? 'mt-4 max-h-[500px] opacity-100'
                : 'mt-0 max-h-0 opacity-0'
            }`}
          >
            {!isUK && (
              <Row LeadingIcon={HashtagIcon} label="HIN/SRN">
                <Typography variant="text-body-sm">
                  {shareholding?.holderIdMasked ?? '-'}
                </Typography>
              </Row>
            )}
            <Row LeadingIcon={MailIcon} label="Email">
              <Typography className="break-words" variant="text-body-sm">
                {shareholding?.email ?? '-'}
              </Typography>
            </Row>
            <Row LeadingIcon={PhoneIcon} label="Phone">
              <Typography variant="text-body-sm">
                {shareholding?.phoneNumber ?? '-'}
              </Typography>
            </Row>
            <Row LeadingIcon={HomeIcon} label="Address">
              <Typography variant="text-body-sm">{formattedAddress}</Typography>
            </Row>
            {!isUK && (
              <Row LeadingIcon={MailIcon} label="Broker">
                <Typography variant="text-body-sm">
                  {shareholding?.brokerNameShort ??
                    shareholding?.brokerPid ??
                    '-'}
                </Typography>
              </Row>
            )}
          </div>
        </section>
        <div className="ml-[-24px] h-px w-[calc(100%+48px)] bg-gray-200" />
        <section>
          <div className="flex items-center justify-between">
            <Typography variant="text-heading-sm">Movements</Typography>
            <div
              className={clsx(
                `ml-2 cursor-pointer transition-transform duration-300`,
                loading && 'hidden'
              )}
              onClick={() => setShowTransactions(!showTransactions)}
            >
              {showTransactions ? (
                <ChevronUpIcon className="h-4 w-4" />
              ) : (
                <ChevronDownIcon className="h-4 w-4" />
              )}
            </div>
          </div>
          <div
            className={`relative transition-[max-height,margin-top,opacity] duration-300 ease-in-out ${
              showTransactions
                ? 'mt-4 max-h-[480px] opacity-100'
                : 'mt-0 max-h-0 opacity-0'
            }`}
          >
            <div
              className="space-y-4 overflow-y-auto pr-1"
              style={{ maxHeight: '420px' }}
              onScroll={handleScrollTransactions}
            >
              {data?.shareholdingSummary?.shareMovements?.map((movement) => {
                if (!movement) return null;
                return (
                  <TransactionItem key={movement?.id} movement={movement} />
                );
              })}
            </div>
            {showTransactions &&
              data?.shareholdingSummary?.shareMovements?.length &&
              data?.shareholdingSummary?.shareMovements?.length > 5 && (
                <>
                  <div
                    className={`pointer-events-none absolute inset-x-0 bottom-0 h-12 bg-gradient-to-t from-white to-transparent transition-opacity duration-300 ${
                      isAtBottomTransactions ? 'opacity-0' : 'opacity-100'
                    }`}
                  />
                  <div
                    className={`pointer-events-none absolute inset-x-0 top-0 h-12 bg-gradient-to-b from-white to-transparent transition-opacity duration-300 ${
                      isAtTopTransactions ? 'opacity-0' : 'opacity-100'
                    }`}
                  />
                </>
              )}
          </div>
          {loading && (
            <div className="space-y-4">
              <Skeleton loading height={20} />
              <Skeleton loading height={20} />
              <Skeleton loading height={20} />
              <Skeleton loading height={20} />
              <Skeleton loading height={20} />
            </div>
          )}
        </section>
        <div
          className={clsx(
            `ml-[-16px] h-px w-[calc(100%+32px)] bg-gray-200`,
            (!children || children.length === 0) && 'hidden'
          )}
        />
        {!!children && children.length > 0 && (
          <section className="z-10">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Typography variant="text-heading-sm">Retail owners</Typography>
                <Badge>
                  {children?.length}
                  {(children && children?.length > 1) || children?.length === 0
                    ? ' accounts'
                    : ' account'}
                </Badge>
              </div>
              <div
                className={clsx(
                  `ml-2 cursor-pointer transition-transform duration-300`
                )}
                onClick={() => setShowRetailOwners(!showRetailOwners)}
              >
                {showRetailOwners ? (
                  <ChevronUpIcon className="h-4 w-4" />
                ) : (
                  <ChevronDownIcon className="h-4 w-4" />
                )}
              </div>
            </div>
            <div
              className={`relative transition-[max-height,margin-top,opacity] duration-300 ease-in-out ${
                showRetailOwners
                  ? 'mt-4 max-h-[480px] opacity-100'
                  : 'pointer-events-none mt-0 max-h-0 opacity-0'
              }`}
            >
              <TextInput
                LeadingIcon={MagnifyingGlassIcon}
                placeholder="Search account name"
                size="sm"
                onChange={(e) => {
                  setSearchQuery(e.target.value);
                }}
              />
              <div className="mb-2 mt-4 h-px w-full bg-gray-200" />

              <div
                className="space-y-4 overflow-y-auto pr-1"
                style={{ maxHeight: '420px' }}
                onScroll={handleScrollRetailHolders}
              >
                {children?.map((b) => {
                  if (!b) return null;

                  return (
                    <div
                      key={`${Number(b?.id) * Math.random()}`}
                      className="space-y-2 border-b border-b-gray-200 pb-3 last:border-0"
                    >
                      <div className="flex items-center justify-between">
                        <Typography variant="text-label-sm">
                          {b?.beneficialOwnerAccount?.accountName}
                        </Typography>
                        <Link
                          className="text-amplify-green-600"
                          href={routes?.investors?.search?.contacts?.contactsV2?.href(
                            marketListingKey,
                            b?.beneficialOwnerAccount?.contact?.id as string
                          )}
                        >
                          <div className="border-b-amplify-green-600 flex items-center gap-2 hover:border-b">
                            <Typography variant="text-button-sm">
                              View
                            </Typography>
                            <ArrowRightIcon className="h-4 w-4" />
                          </div>
                        </Link>
                      </div>
                      <Badge color="moss" size="sm">
                        {b?.children && b?.children.length > 0
                          ? 'Intermediary'
                          : 'Retail holder'}
                      </Badge>
                      <Row LeadingIcon={ScaleIcon} label="Holdings">
                        <div>
                          <Typography variant="text-body-sm">
                            {(b?.shares ?? 0).toLocaleString()}{' '}
                            {b?.shares === 1 ? 'share' : 'shares'}
                          </Typography>
                          <Typography
                            className="text-gray-500"
                            variant="text-body-sm"
                          >
                            Last updated:{' '}
                            {dayjs(b?.updatedAt).format('DD MMM YY')}
                          </Typography>
                        </div>
                      </Row>
                    </div>
                  );
                })}
                {showRetailOwners &&
                  !!children?.length &&
                  children?.length > 2 && (
                    <>
                      <div
                        className={`pointer-events-none absolute inset-x-0 bottom-0 h-12 bg-gradient-to-t from-white to-transparent transition-opacity duration-300 ${
                          isAtBottomRetailHolders ? 'opacity-0' : 'opacity-100'
                        }`}
                      />
                      <div
                        className={`pointer-events-none absolute inset-x-0 top-10 h-12 bg-gradient-to-b from-white to-transparent transition-opacity duration-300 ${
                          isAtTopRetailHolders ? 'opacity-0' : 'opacity-100'
                        }`}
                      />
                    </>
                  )}
              </div>
            </div>
            {loading && (
              <div className="space-y-4">
                <Skeleton loading height={100} variant="rect" />
                <Skeleton loading height={100} variant="rect" />
                <Skeleton loading height={100} variant="rect" />
              </div>
            )}
          </section>
        )}
        <div className="ml-[-24px] h-px w-[calc(100%+48px)] bg-gray-200" />
        <section className="z-20">
          <Button
            destructive
            className="w-full text-gray-700"
            size="md"
            variant="secondary-gray"
            onClick={() => toggleOpenLinkContactModal(true)}
          >
            Relink account
          </Button>
        </section>
      </div>
    );
  }, [
    toggleOpenLinkContactModal,
    shareholding?.accountName,
    shareholding?.holderIdMasked,
    shareholding?.email,
    shareholding?.phoneNumber,
    shareholding?.brokerNameShort,
    shareholding?.brokerPid,
    renderHoldings,
    renderProfitLoss,
    renderTimeHeld,
    renderLastMovement,
    showMoreShareholdingInfo,
    renderTotalBought,
    renderTotalSold,
    renderTotalTrades,
    showContactInfo,
    isUK,
    formattedAddress,
    loading,
    showTransactions,
    data?.shareholdingSummary?.shareMovements,
    isAtBottomTransactions,
    isAtTopTransactions,
    children,
    showRetailOwners,
    isAtBottomRetailHolders,
    isAtTopRetailHolders,
    marketListingKey,
    pathname,
    push,
    query,
  ]);

  return (
    <div className="h-fit overflow-y-clip rounded-lg border border-gray-200 bg-white p-4">
      {renderContent}
    </div>
  );
};

export default SingleShareholdingInfoRightSidebar;

const Row = ({
  LeadingIcon,
  children,
  label,
}: {
  LeadingIcon:
    | ((props: SVGProps<SVGSVGElement>) => ReactElement | null)
    | React.ForwardRefExoticComponent<
        Omit<React.SVGProps<SVGSVGElement>, 'ref'> & {
          title?: string | undefined;
          titleId?: string | undefined;
        } & React.RefAttributes<SVGSVGElement>
      >;
  children: React.ReactNode;
  label: string;
}) => {
  return (
    <div className="grid grid-cols-2 items-start gap-4">
      <div className="flex items-center gap-1">
        {LeadingIcon && <LeadingIcon className="h-4 w-4 min-w-[16px]" />}
        <Typography variant="text-body-sm">{label}</Typography>
      </div>
      {children}
    </div>
  );
};

const TransactionItem = ({ movement }: { movement: ShareMovement }) => {
  const {
    currentCompanyProfileUser: {
      profile: { currency },
    },
  } = useCurrentCompanyProfileUser();

  const renderMovementType = () => {
    if (movement?.movement > 0) {
      return (
        <div className="flex items-center gap-2">
          <Badge color="green" size="sm">
            <div className="flex items-center gap-1">
              <ArrowUpIcon className="h-3 w-3" />
              <Typography variant="text-caption">Bought</Typography>
            </div>
          </Badge>
          <Typography className="text-green-700" variant="text-button-sm">
            +{movement?.movement.toLocaleString()}
          </Typography>
          {movement?.estimatedPrice && (
            <Typography variant="text-button-sm">
              (
              {formatMoney(
                movement?.estimatedPrice
                  ? movement?.movement * movement?.estimatedPrice
                  : 0,
                currency ?? ''
              )}
              )
            </Typography>
          )}
        </div>
      );
    } else if (movement?.movement < 0) {
      return (
        <div className="flex items-center gap-2">
          <Badge color="red" size="sm">
            <div className="flex items-center gap-1">
              <ArrowDownIcon className="h-3 w-3" />
              <Typography variant="text-caption">Sold</Typography>
            </div>
          </Badge>
          <Typography className="text-red-700" variant="text-button-sm">
            {movement?.movement.toLocaleString()}
          </Typography>
          <Typography variant="text-button-sm">
            (
            {formatMoney(
              movement?.estimatedPrice && movement?.movement
                ? movement?.movement * movement?.estimatedPrice
                : 0,
              currency ?? ''
            )}
            )
          </Typography>
        </div>
      );
    } else {
      return null;
    }
  };

  return (
    <div className="space-y-1 border-t pt-2 first:border-t-0 first:pt-0">
      <Typography variant="text-label-sm">
        {dayjs(movement?.settledAt).format('DD MMM YYYY')}
      </Typography>
      {renderMovementType()}
      <Typography className="text-gray-700" variant="text-body-sm">
        {movement?.closingBalance.toLocaleString()} net holdings
      </Typography>
    </div>
  );
};
